import { EntityDamageCause, GameMode, InputPermissionCategory, Player, system } from "@minecraft/server";
import { getDistance } from "../../../utilities/vector3";
import { PIGLIN_CHAMPION_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
// Unused function removed
/**
 * Executes the first phase of the spin slam attack (lifting entities)
 * Applies damage and upward knockback to nearby entities
 *
 * @param piglinChampion The piglin champion entity
 */
export async function executeSpinSlamLiftAttack(piglinChampion) {
    const damageRadius = 5;
    // Use direct damage value instead of percentage
    const damage = PIGLIN_CHAMPION_ATTACK_DAMAGES.spin_slam.damage;
    // Vertical attack parameters - high vertical component for lifting
    const horizontalStrength = 0.5; // Minimal horizontal movement
    const verticalStrength = 1.7; // Strong upward lift (7 blocks)
    // Get direction vector directly from the piglin's view direction
    const viewDirection = piglinChampion.getViewDirection();
    const dirX = viewDirection.x;
    const dirZ = viewDirection.z;
    // Calculate position 3 blocks in front of the piglin as the origin for the attack
    const originPos = {
        x: piglinChampion.location.x + dirX * 3,
        y: piglinChampion.location.y,
        z: piglinChampion.location.z + dirZ * 3
    };
    // Apply damage and lift entities within the radius
    piglinChampion.dimension
        .getEntities({
        location: originPos,
        maxDistance: damageRadius,
        excludeFamilies: ["piglin_champion", "piglin", "rock"]
    })
        .forEach((entity) => {
        // Calculate upward lift
        try {
            // Try to apply knockback first
            if (entity instanceof Player) {
                const gameMode = entity.getGameMode();
                if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                    entity.applyKnockback(0, 0, horizontalStrength, verticalStrength);
                    system.waitTicks(10);
                    entity.addEffect("minecraft:levitation", 25, { amplifier: 0, showParticles: false });
                }
            }
            else {
                system.waitTicks(10);
                entity.applyKnockback(0, 0, horizontalStrength, verticalStrength);
            }
        }
        catch (e) {
            // Fallback to applyImpulse if applyKnockback fails
            const impulse = {
                x: 0,
                y: verticalStrength,
                z: 0
            };
            entity.applyImpulse(impulse);
        }
        // Apply damage after knockback/impulse - only to entities that are not XP orbs or items
        if (entity.typeId !== "minecraft:xp_orb" && entity.typeId !== "minecraft:item") {
            entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });
        }
    });
}
/**
 * Executes the second phase of the spin slam attack (slamming entities down)
 * Applies damage, knockback, and camera shake to entities on the ground
 * Also applies a stun effect, keeping entities in place for 2 seconds
 *
 * @param piglinChampion The piglin champion entity
 * @returns A promise that resolves when the stun effect is complete
 */
export async function executeSpinSlamGroundAttack(piglinChampion) {
    const damageRadius = 5;
    // Use direct damage value instead of percentage
    const damage = PIGLIN_CHAMPION_ATTACK_DAMAGES.spin_slam.damage;
    // Get direction vector directly from the piglin's view direction
    const viewDirection = piglinChampion.getViewDirection();
    const dirX = viewDirection.x;
    const dirZ = viewDirection.z;
    // Calculate position 3 blocks in front of the piglin as the origin for the attack
    const originPos = {
        x: piglinChampion.location.x + dirX * 3,
        y: piglinChampion.location.y,
        z: piglinChampion.location.z + dirZ * 3
    };
    // Store affected entities for the stun effect
    const affectedEntities = [];
    // Apply damage and knockback to entities on the ground
    piglinChampion.dimension
        .getEntities({
        location: originPos,
        maxDistance: damageRadius,
        excludeFamilies: ["piglin_champion", "piglin", "rock", "inanimate"]
    })
        .forEach((entity) => {
        // Calculate direction from origin to entity for knockback
        const dx = entity.location.x - originPos.x;
        const dz = entity.location.z - originPos.z;
        // Calculate distance for normalization
        const distance = Math.sqrt(dx * dx + dz * dz);
        if (distance > 0) {
            // Normalize the direction vector
            const nx = dx / distance;
            const nz = dz / distance;
            // Knockback parameters
            const horizontalStrength = 3.0; // 3 blocks of knockback
            const verticalStrength = 0.7;
            try {
                // Try to apply knockback first
                if (entity instanceof Player) {
                    const gameMode = entity.getGameMode();
                    if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                        entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                    }
                }
                else {
                    entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                }
            }
            catch (e) {
                // Fallback to applyImpulse if applyKnockback fails
                const impulse = {
                    x: nx * horizontalStrength,
                    y: verticalStrength,
                    z: nz * horizontalStrength
                };
                entity.applyImpulse(impulse);
            }
        }
        // Apply damage after knockback/impulse - only to entities that are not XP orbs or items
        if (entity.typeId !== "minecraft:xp_orb" && entity.typeId !== "minecraft:item") {
            entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });
            // Store entity for the stun effect
            affectedEntities.push(entity);
        }
    });
    // Apply camera shake to nearby players
    applyCameraShake(piglinChampion);
    // Delay the stun effect by 20 ticks
    await system.waitTicks(20);
    // After waiting, separate players from non-player entities
    const entitiesWithLocations = [];
    const affectedPlayers = [];
    for (const entity of affectedEntities) {
        try {
            // Only include entities that still exist
            if (entity instanceof Player) {
                // For players, just store the entity reference
                affectedPlayers.push(entity);
                // Disable movement at the start (only once)
                entity.inputPermissions.setPermissionCategory(InputPermissionCategory.Movement, false);
            }
            else {
                // For non-player entities, store entity and location for teleportation
                entitiesWithLocations.push({
                    entity: entity,
                    location: { x: entity.location.x, y: entity.location.y, z: entity.location.z }
                });
            }
        }
        catch (e) {
            // Entity might have been removed during the wait
        }
    }
    // Apply stun effect for 2 seconds (40 ticks)
    const stunDurationTicks = 40;
    let ticksElapsed = 0;
    // Return a promise that resolves when the stun effect is complete
    return new Promise((resolve) => {
        const intervalId = system.runInterval(() => {
            // Teleport non-player entities back to their saved locations
            for (const { entity, location } of entitiesWithLocations) {
                try {
                    // Teleport entity, ensuring it's on the ground
                    const groundPosition = findGroundPosition(entity.dimension, location);
                    entity.teleport(groundPosition);
                }
                catch (e) {
                    // Silently handle errors (entity might have died)
                }
            }
            // Increment tick counter
            ticksElapsed++;
            // Clear interval after duration is reached
            if (ticksElapsed >= stunDurationTicks) {
                system.clearRun(intervalId);
                // Re-enable movement for all affected players
                for (const player of affectedPlayers) {
                    try {
                        player.inputPermissions.setPermissionCategory(InputPermissionCategory.Movement, true);
                    }
                    catch (e) {
                        // Silently handle errors (player might have disconnected)
                    }
                }
                resolve();
            }
        }, 1); // Run every tick
    });
}
/**
 * Finds the ground position at a specific X/Z coordinate
 * Ensures the entity is placed on top of a solid block with air above it
 * @param dimension The dimension to check in
 * @param location The location to check from
 * @returns A Vector3 position on the ground
 */
function findGroundPosition(dimension, location) {
    // Start from the entity's current position
    const startY = location.y;
    const maxSearchDistance = 10; // Maximum blocks to search down
    // Search downward for a non-air block
    for (let y = 0; y <= maxSearchDistance; y++) {
        const checkPos = {
            x: location.x,
            y: startY - y,
            z: location.z
        };
        // Get the block at this position
        const block = dimension.getBlock(checkPos);
        // If we found a non-air block, check if it can be stood on
        if (block && !block.isAir) {
            // Check if the block above is air (to ensure entity can fit)
            const blockAbove = dimension.getBlock({
                x: location.x,
                y: startY - y + 1,
                z: location.z
            });
            // Only return this position if the block above is air
            if (blockAbove && blockAbove.isAir) {
                return {
                    x: location.x,
                    y: startY - y + 1, // Position exactly at the bottom of the air block
                    z: location.z
                };
            }
            // If the block above isn't air, continue searching downward
        }
    }
    // If we couldn't find a suitable ground position, return the original location
    return location;
}
/**
 * Applies camera shake to players within a radius of the entity
 * @param source The entity causing the camera shake
 */
function applyCameraShake(source) {
    const radius = 32; // 32 block radius for camera shake
    const minStrength = 0.02;
    const maxStrength = 0.5;
    const length = 0.5;
    const sourceLocation = source.location;
    const dimension = source.dimension;
    // Get all players within the radius
    const players = dimension.getPlayers({
        location: sourceLocation,
        maxDistance: radius
    });
    // Apply camera shake to each player
    players.forEach((player) => {
        // Only apply to players on the ground
        if (!player.isOnGround)
            return;
        // Calculate distance from source
        const distance = getDistance(sourceLocation, player.location);
        // Skip if outside radius (should be handled by getPlayers, but just in case)
        if (distance > radius)
            return;
        // Calculate strength based on distance (inversely proportional)
        // Players closer to the source get stronger shake
        const strengthMultiplier = 1 - distance / radius; // 0 at edge, 1 at center
        const strength = minStrength + strengthMultiplier * (maxStrength - minStrength);
        // Apply camera shake using /camerashake command with player's name
        player.runCommand(`camerashake add @s ${strength.toFixed(2)} ${length.toFixed(2)} positional`);
    });
    return;
}
/**
 * Attack timing constants for spin slam attack phases
 */
const LIFT_ATTACK_TIMING = 75; // First phase: lift entities at tick 75
const SLAM_ATTACK_TIMING = 121; // Second phase: slam entities at tick 134
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 179;
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Executes the spin slam attack for the Piglin Champion using the new timing system
 * This is a two-phase attack with lifting and slamming components
 * Uses localized runTimeout for both attack phases, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeSpinSlamAttack(piglinChampion) {
    // Phase 1: Lift attack at tick 44
    let liftTiming = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(liftTiming);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "spin_slam") {
                executeSpinSlamLiftAttack(piglinChampion);
            }
        }
        catch (error) {
            system.clearRun(liftTiming);
        }
    }, LIFT_ATTACK_TIMING);
    // Phase 2: Slam attack at tick 134
    let slamTiming = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(slamTiming);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "spin_slam") {
                executeSpinSlamGroundAttack(piglinChampion);
            }
        }
        catch (error) {
            system.clearRun(slamTiming);
        }
    }, SLAM_ATTACK_TIMING);
    // Reset the attack state to "none" after the animation is complete
    let reset = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (isDead) {
                system.clearRun(reset);
                return;
            }
            // Only reset if the attack is still "spin_slam" - prevents interference with stuns
            if (currentAttack === "spin_slam") {
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
            system.clearRun(reset);
        }
        catch (error) {
            system.clearRun(reset);
        }
    }, ANIMATION_TIME);
    // Wait for cooldown, then set cooldown property to false to select the next attack
    let cooldown = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldown);
                return;
            }
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
            system.clearRun(cooldown);
        }
        catch (error) {
            system.clearRun(cooldown);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
    return;
}
