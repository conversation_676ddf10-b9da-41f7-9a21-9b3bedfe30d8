import { system } from "@minecraft/server";
import { getRandomLocation, getDistance } from "../../../utilities/vector3";
import { countPiglinChampionMinions } from "../controller";
/**
 * Generates a random single minion for summoning
 * @returns Array of entity configurations for spawning (single minion)
 */
function generateMinionCombination() {
    const minionTypes = [
        "ptd_dbb:piglin_brute",
        "ptd_dbb:piglin_marauder"
    ];
    // Randomly select one minion type
    const randomIndex = Math.floor(Math.random() * minionTypes.length);
    const selectedMinionType = minionTypes[randomIndex];
    // Return single minion configuration
    return [{ entityId: selectedMinionType, count: 1 }];
}
/**
 * Attack timing in ticks - when the damage should be applied during the animation
 */
const ATTACK_TIMING = 40;
/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 125;
/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;
/**
 * Executes the summoning chant attack for the Piglin Champion using the new timing system
 * Uses localized runTimeout for attack timing, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 * @param target The target entity (optional)
 */
export function executeSummoningChantAttack(piglinChampion, target) {
    // Wait for the attack timing before executing the attack
    let timing = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(timing);
                return;
            }
            if (piglinChampion.getProperty("ptd_dbb:attack") === "summoning_chant") {
                performSummoningChantAttack(piglinChampion, target);
            }
        }
        catch (error) {
            system.clearRun(timing);
        }
    }, ATTACK_TIMING);
    // Reset the attack state to "none" after the animation is complete
    let reset = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            if (isDead) {
                system.clearRun(reset);
                return;
            }
            // Only reset if the attack is still "summoning_chant" - prevents interference with stuns
            if (currentAttack === "summoning_chant") {
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
            system.clearRun(reset);
        }
        catch (error) {
            system.clearRun(reset);
        }
    }, ANIMATION_TIME);
    // Wait for cooldown, then set cooldown property to false to select the next attack
    let cooldown = system.runTimeout(() => {
        try {
            const isDead = piglinChampion.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldown);
                return;
            }
            piglinChampion.setProperty("ptd_dbb:cooling_down", false);
            system.clearRun(cooldown);
        }
        catch (error) {
            system.clearRun(cooldown);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
    return;
}
/**
 * Maximum number of minions that can be active at once
 */
const MAX_MINIONS = 3;
/**
 * Delay between minion spawns in ticks
 */
const SPAWN_DELAY = 15;
/**
 * Performs the actual summoning chant attack
 * @param piglinChampion The piglin champion entity
 * @param target The target entity (optional)
 */
async function performSummoningChantAttack(piglinChampion, target) {
    // Get the piglin champion's unique ID for tagging minions
    const championId = piglinChampion.id;
    // Generate a random combination of minions and create a spawn queue
    const entityConfigs = generateMinionCombination();
    const spawnQueue = [];
    // Build spawn queue with individual minions (1 per spawn)
    entityConfigs.forEach(config => {
        for (let i = 0; i < config.count; i++) {
            spawnQueue.push(config.entityId);
        }
    });
    // Determine spawn location based on target distance
    let spawnAroundTarget = false;
    if (target) {
        const distance = getDistance(piglinChampion.location, target.location);
        // If target is in unreachable range (12-32 blocks), spawn around target
        spawnAroundTarget = distance >= 12 && distance <= 32;
    }
    // Spawn minions one at a time with interval checking
    await spawnMinionsWithLimitCheck(piglinChampion, spawnQueue, spawnAroundTarget, target, championId);
    return;
}
/**
 * Spawns minions one at a time while checking the current minion count
 * @param piglinChampion The piglin champion entity
 * @param spawnQueue Array of entity IDs to spawn
 * @param spawnAroundTarget Whether to spawn around target or champion
 * @param target The target entity (optional)
 * @param championId The champion's unique ID for tagging
 */
async function spawnMinionsWithLimitCheck(piglinChampion, spawnQueue, spawnAroundTarget, target, championId) {
    return new Promise((resolve) => {
        let currentIndex = 0;
        const spawnInterval = system.runInterval(() => {
            try {
                // Check if we've processed all minions in the queue
                if (currentIndex >= spawnQueue.length) {
                    system.clearRun(spawnInterval);
                    resolve();
                    return;
                }
                // Check current minion count
                const currentMinionCount = countPiglinChampionMinions(piglinChampion, 64);
                // If we've reached the maximum, skip spawning but continue the interval
                if (currentMinionCount >= MAX_MINIONS) {
                    currentIndex++;
                    return;
                }
                // Get the entity ID to spawn
                const entityId = spawnQueue[currentIndex];
                // Choose spawn location based on whether target is in unreachable range
                const baseLocation = spawnAroundTarget && target ? target.location : piglinChampion.location;
                // Use getRandomLocation to get a random position around the chosen entity
                const pos = getRandomLocation(baseLocation, piglinChampion.dimension, 3, // Base offset (minimum distance from chosen entity)
                4, // Additional offset (random extra distance)
                0, // No Y offset
                true // Check for air block
                );
                // If we got a valid position, spawn the minion
                if (pos) {
                    // Add visual effects before spawning
                    piglinChampion.dimension.spawnParticle("minecraft:large_explosion", pos);
                    // Spawn the entity
                    const entity = piglinChampion.dimension.spawnEntity(entityId, pos);
                    if (entity) {
                        // Tag the minion with the piglin champion's ID immediately after spawning
                        entity.setDynamicProperty("ptd_dbb:champion_id", championId);
                        // Play a sound and visual effect
                        piglinChampion.dimension.playSound("random.totem", entity.location, { volume: 200, pitch: 0.9 });
                        piglinChampion.dimension.spawnParticle("ptd_dbb:pg_summon2_01", entity.location);
                    }
                }
                // Move to next minion in queue
                currentIndex++;
            }
            catch (error) {
                console.warn(`Failed to spawn minion in summoning chant: ${error}`);
                system.clearRun(spawnInterval);
                resolve();
            }
        }, SPAWN_DELAY);
    });
}
