import { Entity, system } from "@minecraft/server";
import { EntityQuantityConfig, spawnEntitiesWithInterval } from "../../../utilities/summonEntity";
import { getRandomLocation, getDistance } from "../../../utilities/vector3";
import { countPiglinChampionMinions } from "../controller";

/**
 * Generates a random minion type for summoning
 * @returns The entity ID of the selected minion type
 */
function generateRandomMinionType(): string {
  const minionTypes = [
    "ptd_dbb:piglin_brute",
    "ptd_dbb:piglin_marauder"
  ];

  // Randomly select one minion type
  const randomIndex = Math.floor(Math.random() * minionTypes.length);
  return minionTypes[randomIndex]!;
}

/**
 * Attack timing in ticks - when the damage should be applied during the animation
 */
const ATTACK_TIMING = 40;

/**
 * Total animation time in ticks
 */
const ANIMATION_TIME = 125;

/**
 * Cooldown time in ticks after the attack completes
 */
const COOLDOWN_TIME = 20;

/**
 * Executes the summoning chant attack for the Piglin Champion using the new timing system
 * Uses localized runTimeout for attack timing, reset, and cooldown
 *
 * @param piglinChampion The piglin champion entity
 * @param target The target entity (optional)
 */
export function executeSummoningChantAttack(piglinChampion: Entity, target?: Entity): void {
  // Wait for the attack timing before executing the attack
  let timing = system.runTimeout(() => {
    try {
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(timing);
        return;
      }

      if (piglinChampion.getProperty("ptd_dbb:attack") === "summoning_chant") {
        performSummoningChantAttack(piglinChampion, target);
      }
    } catch (error) {
      system.clearRun(timing);
    }
  }, ATTACK_TIMING);

  // Reset the attack state to "none" after the animation is complete
  let reset = system.runTimeout(() => {
    try {
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;
      const currentAttack = piglinChampion.getProperty("ptd_dbb:attack") as string;

      if (isDead) {
        system.clearRun(reset);
        return;
      }

      // Only reset if the attack is still "summoning_chant" - prevents interference with stuns
      if (currentAttack === "summoning_chant") {
        piglinChampion.triggerEvent("ptd_dbb:reset_attack");
      }

      system.clearRun(reset);
    } catch (error) {
      system.clearRun(reset);
    }
  }, ANIMATION_TIME);

  // Wait for cooldown, then set cooldown property to false to select the next attack
  let cooldown = system.runTimeout(() => {
    try {
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldown);
        return;
      }
      piglinChampion.setProperty("ptd_dbb:cooling_down", false);
      system.clearRun(cooldown);
    } catch (error) {
      system.clearRun(cooldown);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);

  return;
}

/**
 * Maximum number of minions that can be active at once
 */
const MAX_MINIONS = 3;

/**
 * Delay between minion spawns in ticks
 */
const SPAWN_DELAY = 15;

/**
 * Performs the actual summoning chant attack
 * @param piglinChampion The piglin champion entity
 * @param target The target entity (optional)
 */
async function performSummoningChantAttack(piglinChampion: Entity, target?: Entity): Promise<void> {
  // Get the piglin champion's unique ID for tagging minions
  const championId = piglinChampion.id;

  // Count current minions and calculate how many are needed
  const currentMinionCount = countPiglinChampionMinions(piglinChampion, 64);
  const minionsNeeded = Math.max(0, MAX_MINIONS - currentMinionCount);

  // If no minions are needed, return early
  if (minionsNeeded === 0) {
    return;
  }

  // Determine spawn location based on target distance
  let spawnAroundTarget = false;
  if (target) {
    const distance = getDistance(piglinChampion.location, target.location);
    // If target is in unreachable range (12-32 blocks), spawn around target
    spawnAroundTarget = distance >= 12 && distance <= 32;
  }

  // Choose spawn location base entity
  const baseLocation = spawnAroundTarget && target ? target.location : piglinChampion.location;

  // Function to get spawn location for each minion
  const getSpawnLocation = () => {
    const location = getRandomLocation(
      baseLocation,
      piglinChampion.dimension,
      3, // Base offset (minimum distance from chosen entity)
      4, // Additional offset (random extra distance)
      0, // No Y offset
      true // Check for air block
    );

    // Add explosion particle effect at spawn location
    if (location) {
      piglinChampion.dimension.spawnParticle("minecraft:large_explosion", location);
    }

    return location;
  };

  // Function to handle entity spawning with effects and tagging
  const onEntitySpawned = (entity: Entity) => {
    if (entity) {
      // Tag the minion with the piglin champion's ID immediately after spawning
      entity.setDynamicProperty("ptd_dbb:champion_id", championId);

      // Play a sound and visual effect
      piglinChampion.dimension.playSound("random.totem", entity.location, {volume: 200, pitch: 0.9});
      piglinChampion.dimension.spawnParticle("ptd_dbb:pg_summon2_01", entity.location);
    }
  };

  // If only 1 minion is needed, use the regular spawn function
  if (minionsNeeded === 1) {
    const minionType = generateRandomMinionType();
    const spawnPos = getSpawnLocation();

    if (spawnPos) {
      // Add visual effects before spawning
      piglinChampion.dimension.spawnParticle("minecraft:large_explosion", spawnPos);

      // Spawn the entity directly
      const spawnedEntity = piglinChampion.dimension.spawnEntity(minionType, spawnPos);

      // Apply effects and tagging
      if (spawnedEntity) {
        onEntitySpawned(spawnedEntity);
      }
    }
  } else {
    // If multiple minions are needed, use the spawnEntitiesWithInterval utility
    const entityConfigs: EntityQuantityConfig[] = [];

    // Create configurations for the needed minions
    for (let i = 0; i < minionsNeeded; i++) {
      const minionType = generateRandomMinionType();
      entityConfigs.push({ entityId: minionType, count: 1 });
    }

    // Spawn entities with interval using the utility function
    await spawnEntitiesWithInterval(
      piglinChampion.dimension,
      entityConfigs,
      getSpawnLocation,
      SPAWN_DELAY,
      onEntitySpawned
    );
  }

  return;
}


